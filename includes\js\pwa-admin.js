/**
 * PWA Admin JavaScript - Enhanced functionality for PWA settings page
 */

jQuery(document).ready(function ($) {
    'use strict';

    // Tab functionality with URL hash support
    function initTabs() {
        // Handle tab clicks
        $('.q-pwa-tab-button').on('click', function (e) {
            e.preventDefault();
            const tabId = $(this).data('tab');
            switchToTab(tabId);
            
            // Update URL hash
            window.location.hash = tabId;
        });

        // Handle initial tab from URL hash
        const hash = window.location.hash.substring(1);
        if (hash && $('#tab-' + hash).length) {
            switchToTab(hash);
        }

        // Handle browser back/forward
        $(window).on('hashchange', function () {
            const hash = window.location.hash.substring(1);
            if (hash && $('#tab-' + hash).length) {
                switchToTab(hash);
            }
        });
    }

    function switchToTab(tabId) {
        // Remove active class from all tabs and buttons
        $('.q-pwa-tab-button').removeClass('active');
        $('.q-pwa-tab-content').removeClass('active');

        // Add active class to clicked button and corresponding tab
        $('[data-tab="' + tabId + '"]').addClass('active');
        $('#tab-' + tabId).addClass('active');

        // Scroll to top of tab content
        $('.q-pwa-tabs-container')[0].scrollIntoView({ behavior: 'smooth' });
    }

    // Enhanced manifest testing
    function initManifestTesting() {
        $('#q-test-manifest').on('click', function () {
            const $button = $(this);
            const $preview = $('#q-manifest-preview');
            const $content = $('#q-manifest-content');
            const manifestUrl = qPWAAdmin.manifestUrl;

            // Show loading state
            $button.prop('disabled', true).text('Testing...');

            $.get(manifestUrl)
                .done(function (data) {
                    // Format and display manifest
                    $content.text(JSON.stringify(data, null, 2));
                    $preview.slideDown();

                    // Validate manifest
                    validateManifest(data);

                    // Show success message
                    showNotification('Manifest loaded successfully!', 'success');
                })
                .fail(function (xhr) {
                    let errorMsg = 'Failed to load manifest.';
                    if (xhr.status === 404) {
                        errorMsg = 'Manifest file not found. Make sure PWA is enabled.';
                    } else if (xhr.status === 500) {
                        errorMsg = 'Server error loading manifest. Check your settings.';
                    }
                    showNotification(errorMsg, 'error');
                })
                .always(function () {
                    $button.prop('disabled', false).html('<span class="dashicons dashicons-search"></span> Test Manifest');
                });
        });
    }

    // Basic manifest validation
    function validateManifest(manifest) {
        const issues = [];
        
        if (!manifest.name || manifest.name.length === 0) {
            issues.push('App name is missing');
        }
        
        if (!manifest.short_name || manifest.short_name.length === 0) {
            issues.push('Short name is missing');
        }
        
        if (!manifest.icons || manifest.icons.length === 0) {
            issues.push('No icons defined');
        } else {
            const hasRequiredSizes = manifest.icons.some(icon => 
                icon.sizes && (icon.sizes.includes('192x192') || icon.sizes.includes('512x512'))
            );
            if (!hasRequiredSizes) {
                issues.push('Missing required icon sizes (192x192 or 512x512)');
            }
        }
        
        if (!manifest.start_url) {
            issues.push('Start URL is missing');
        }

        // Display validation results
        if (issues.length > 0) {
            const issuesList = issues.map(issue => `<li>${issue}</li>`).join('');
            $('#q-manifest-preview').append(`
                <div class="notice notice-warning inline" style="margin-top: 15px;">
                    <h4>Validation Issues:</h4>
                    <ul>${issuesList}</ul>
                </div>
            `);
        } else {
            $('#q-manifest-preview').append(`
                <div class="notice notice-success inline" style="margin-top: 15px;">
                    <p><strong>✓ Manifest validation passed!</strong></p>
                </div>
            `);
        }
    }

    // Enhanced PWA preview
    function initPWAPreview() {
        $('#q-preview-pwa').on('click', function () {
            const $button = $(this);
            $button.prop('disabled', true);
            
            // Open in new window with PWA-like features
            const previewWindow = window.open(
                qPWAAdmin.siteUrl,
                'pwa-preview',
                'width=375,height=667,scrollbars=yes,resizable=yes,toolbar=no,menubar=no'
            );
            
            if (previewWindow) {
                showNotification('PWA preview opened in new window', 'success');
            } else {
                showNotification('Please allow popups to preview PWA', 'warning');
            }
            
            setTimeout(() => {
                $button.prop('disabled', false);
            }, 1000);
        });
    }

    // Enhanced media upload with validation
    function initMediaUpload() {
        $('.q-media-upload').on('click', function () {
            const field = $(this).data('field');
            const $input = $('input[name="' + field + '"]');
            const $button = $(this);

            const mediaUploader = wp.media({
                title: 'Select App Icon',
                button: {
                    text: 'Use this image'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });

            mediaUploader.on('select', function () {
                const attachment = mediaUploader.state().get('selection').first().toJSON();
                
                // Validate image dimensions
                const img = new Image();
                img.onload = function () {
                    const expectedSize = field.includes('192') ? 192 : 512;
                    
                    if (this.width !== expectedSize || this.height !== expectedSize) {
                        showNotification(
                            `Warning: Icon should be ${expectedSize}x${expectedSize} pixels. Current size: ${this.width}x${this.height}`,
                            'warning'
                        );
                    }
                    
                    // Update input and preview
                    $input.val(attachment.url);
                    updateImagePreview($input, attachment.url);
                    
                    showNotification('Icon uploaded successfully!', 'success');
                };
                
                img.src = attachment.url;
            });

            mediaUploader.open();
        });
    }

    function updateImagePreview($input, url) {
        const $preview = $input.siblings('img');
        if ($preview.length) {
            $preview.attr('src', url);
        } else {
            $input.after(`<br><img src="${url}" style="max-width: 100px; max-height: 100px; margin-top: 10px; border-radius: 4px; border: 1px solid #ddd;" />`);
        }
    }

    // Form validation
    function initFormValidation() {
        $('#q-pwa-settings-form').on('submit', function (e) {
            const errors = [];
            
            // Validate app name length
            const appName = $('input[name="q_pwa_app_name"]').val();
            if (appName && appName.length > 45) {
                errors.push('App name must be 45 characters or less');
            }
            
            // Validate short name length
            const shortName = $('input[name="q_pwa_app_short_name"]').val();
            if (shortName && shortName.length > 12) {
                errors.push('Short name must be 12 characters or less');
            }
            
            if (errors.length > 0) {
                e.preventDefault();
                showNotification('Please fix the following errors:\n• ' + errors.join('\n• '), 'error');
                return false;
            }
            
            // Show saving state
            const $submitButton = $('#submit');
            $submitButton.prop('disabled', true).val('Saving...');
        });
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="notice notice-${type} is-dismissible" style="margin: 15px 0;">
                <p>${message}</p>
                <button type="button" class="notice-dismiss">
                    <span class="screen-reader-text">Dismiss this notice.</span>
                </button>
            </div>
        `);
        
        $('.q-pwa-tabs-container').before($notification);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $notification.fadeOut(() => $notification.remove());
        }, 5000);
        
        // Handle manual dismiss
        $notification.find('.notice-dismiss').on('click', function () {
            $notification.fadeOut(() => $notification.remove());
        });
    }

    // Auto-save functionality
    function initAutoSave() {
        let saveTimeout;
        
        $('#q-pwa-settings-form input, #q-pwa-settings-form select, #q-pwa-settings-form textarea').on('change', function () {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                // Could implement auto-save here if needed
                console.log('Settings changed - consider auto-save');
            }, 2000);
        });
    }

    // Initialize all functionality
    function init() {
        initTabs();
        initManifestTesting();
        initPWAPreview();
        initMediaUpload();
        initFormValidation();
        initAutoSave();
        
        // Show welcome message for first-time users
        if (qPWAAdmin.isFirstTime) {
            showNotification('Welcome to PWA Settings! Start by enabling PWA in the General tab.', 'info');
        }
    }

    // Start everything when DOM is ready
    init();
});
