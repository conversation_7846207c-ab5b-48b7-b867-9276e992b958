/**
 * PWA Admin Styles - Tabbed interface and enhanced UI for PWA settings
 */

/* Main Admin Container */
.q-pwa-admin {
    max-width: 1200px;
}

/* Status Dashboard */
.q-pwa-status-dashboard {
    margin-bottom: 30px;
}

.q-pwa-status-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.status-header h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #1d2327;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.ready {
    background: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
}

.status-badge.incomplete {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.q-pwa-progress {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 24px;
    background: #f1f1f1;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 8px;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00a32a, #4caf50);
    transition: width 0.5s ease;
    border-radius: 12px;
}

.progress-text {
    margin: 0;
    font-weight: 600;
    color: #1d2327;
}

/* Requirements Checklist */
.q-pwa-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.q-pwa-requirements li {
    padding: 12px 0;
    border-bottom: 1px solid #f1f1f1;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.q-pwa-requirements li:last-child {
    border-bottom: none;
}

.q-pwa-requirements li.completed {
    color: #00a32a;
}

.q-pwa-requirements li.pending {
    color: #d63638;
}

.q-pwa-requirements .dashicons {
    margin-top: 2px;
    flex-shrink: 0;
}

.q-pwa-requirements small {
    display: block;
    color: #666;
    font-style: italic;
    margin-top: 4px;
}

/* Tabbed Interface */
.q-pwa-tabs-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.q-pwa-tabs-nav {
    display: flex;
    background: #f6f7f7;
    border-bottom: 1px solid #ccd0d4;
    margin: 0;
    padding: 0;
}

.q-pwa-tab-button {
    background: none;
    border: none;
    padding: 16px 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #50575e;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
    position: relative;
}

.q-pwa-tab-button:hover {
    background: #fff;
    color: #1d2327;
}

.q-pwa-tab-button.active {
    background: #fff;
    color: #2271b1;
    border-bottom-color: #2271b1;
}

.q-pwa-tab-button .dashicons {
    font-size: 16px;
}

/* Tab Content */
.q-pwa-tab-content {
    display: none;
    padding: 30px;
}

.q-pwa-tab-content.active {
    display: block;
}

.q-pwa-tab-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f1f1f1;
}

.q-pwa-tab-header h2 {
    margin: 0 0 8px 0;
    color: #1d2327;
    font-size: 24px;
}

.q-pwa-tab-header p {
    margin: 0;
    color: #646970;
    font-size: 14px;
}

/* Form Styling */
.q-pwa-tab-content .form-table {
    margin-top: 0;
}

.q-pwa-tab-content .form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
    font-weight: 600;
    color: #1d2327;
}

.q-pwa-tab-content .form-table td {
    padding: 20px 10px;
}

.q-pwa-tab-content .form-table tr {
    border-bottom: 1px solid #f1f1f1;
}

.q-pwa-tab-content .form-table tr:last-child {
    border-bottom: none;
}

/* Actions */
.q-pwa-actions {
    margin: 30px 0 0 0;
    padding: 20px 30px;
    background: #f6f7f7;
    border-top: 1px solid #ccd0d4;
}

.q-pwa-actions .button {
    margin-right: 10px;
}

/* Testing Tools */
.q-pwa-testing-tools {
    margin-bottom: 30px;
}

.q-pwa-testing-tools h3 {
    margin-top: 0;
    color: #1d2327;
}

.q-pwa-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
}

.q-pwa-tools .button {
    display: flex;
    align-items: center;
    gap: 6px;
}

.q-pwa-tools .button .dashicons {
    font-size: 16px;
}

/* Installation Guide */
.q-pwa-installation-guide {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #f1f1f1;
}

.q-install-instructions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.instruction-group {
    background: #f9f9f9;
    border: 1px solid #e1e1e1;
    border-radius: 6px;
    padding: 20px;
}

.instruction-group h4 {
    margin: 0 0 15px 0;
    color: #1d2327;
    font-size: 16px;
}

.instruction-group ol {
    margin: 0;
    padding-left: 20px;
}

.instruction-group li {
    margin-bottom: 8px;
    color: #50575e;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .q-pwa-tabs-nav {
        flex-wrap: wrap;
    }
    
    .q-pwa-tab-button {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .q-pwa-tabs-nav {
        flex-direction: column;
    }
    
    .q-pwa-tab-button {
        border-bottom: none;
        border-right: 3px solid transparent;
    }
    
    .q-pwa-tab-button.active {
        border-bottom: none;
        border-right-color: #2271b1;
    }
    
    .q-pwa-tab-content {
        padding: 20px;
    }
    
    .q-install-instructions {
        grid-template-columns: 1fr;
    }
    
    .status-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .q-pwa-tab-button {
        padding: 12px 16px;
        font-size: 13px;
    }
    
    .q-pwa-tab-content {
        padding: 15px;
    }
    
    .q-pwa-tab-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
    }
    
    .q-pwa-tab-header h2 {
        font-size: 20px;
    }
}
