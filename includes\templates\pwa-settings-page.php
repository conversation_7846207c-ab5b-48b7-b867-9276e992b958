<?php
if (!defined('ABSPATH')) {
    exit;
}

// Check user capabilities
if (!current_user_can('manage_options')) {
    return;
}

// Get PWA status
$pwa_status = Q_PWA_Settings::get_pwa_status();
$manifest_validation = Q_PWA_Manifest::validate_manifest();
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <!-- PWA Status Dashboard -->
    <div class="q-pwa-status-dashboard" style="margin-bottom: 20px;">
        <div class="card">
            <h2>PWA Status</h2>
            <div class="q-pwa-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo esc_attr($pwa_status['percentage']); ?>%"></div>
                </div>
                <p><?php echo esc_html($pwa_status['percentage']); ?>% Complete</p>
            </div>
            
            <div class="q-pwa-requirements">
                <h3>Requirements Checklist</h3>
                <ul>
                    <li class="<?php echo $pwa_status['requirements']['https'] ? 'completed' : 'pending'; ?>">
                        <span class="dashicons <?php echo $pwa_status['requirements']['https'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                        HTTPS Enabled
                        <?php if (!$pwa_status['requirements']['https']): ?>
                            <small>Your site must use HTTPS for PWA functionality</small>
                        <?php endif; ?>
                    </li>
                    <li class="<?php echo $pwa_status['requirements']['manifest'] ? 'completed' : 'pending'; ?>">
                        <span class="dashicons <?php echo $pwa_status['requirements']['manifest'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                        PWA Enabled
                        <?php if (!$pwa_status['requirements']['manifest']): ?>
                            <small>Enable PWA functionality below</small>
                        <?php endif; ?>
                    </li>
                    <li class="<?php echo $pwa_status['requirements']['service_worker'] ? 'completed' : 'pending'; ?>">
                        <span class="dashicons <?php echo $pwa_status['requirements']['service_worker'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                        Service Worker Installed
                        <?php if (!$pwa_status['requirements']['service_worker']): ?>
                            <small>Service worker is required for offline functionality</small>
                        <?php endif; ?>
                    </li>
                    <li class="<?php echo $pwa_status['requirements']['icons'] ? 'completed' : 'pending'; ?>">
                        <span class="dashicons <?php echo $pwa_status['requirements']['icons'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                        App Icons Configured
                        <?php if (!$pwa_status['requirements']['icons']): ?>
                            <small>Upload 192x192 and 512x512 icons below</small>
                        <?php endif; ?>
                    </li>
                </ul>
            </div>
            
            <?php if ($pwa_status['ready']): ?>
                <div class="notice notice-success inline">
                    <p><strong>🎉 Your PWA is ready!</strong> Users can now install your app on their devices.</p>
                </div>
            <?php else: ?>
                <div class="notice notice-warning inline">
                    <p><strong>⚠️ PWA Setup Incomplete</strong> Complete the requirements above to enable full PWA functionality.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Manifest Validation -->
    <?php if (!$manifest_validation['valid']): ?>
        <div class="notice notice-error">
            <h3>Manifest Validation Errors</h3>
            <ul>
                <?php foreach ($manifest_validation['errors'] as $error): ?>
                    <li><?php echo esc_html($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Settings Form -->
    <form action="options.php" method="post" id="q-pwa-settings-form">
        <?php
        settings_fields('q_pwa_settings');
        do_settings_sections('q_pwa_settings');
        ?>
        
        <div class="q-pwa-actions">
            <?php submit_button('Save PWA Settings', 'primary', 'submit', false); ?>
            <button type="button" class="button" id="q-test-manifest">Test Manifest</button>
            <button type="button" class="button" id="q-preview-pwa">Preview PWA</button>
        </div>
    </form>

    <!-- PWA Testing Tools -->
    <div class="card" style="margin-top: 20px;">
        <h2>PWA Testing Tools</h2>
        <p>Use these tools to test your PWA configuration:</p>
        
        <div class="q-pwa-tools">
            <a href="<?php echo esc_url(Q_PWA_Manifest::get_manifest_url()); ?>" target="_blank" class="button">
                View Manifest
            </a>
            <a href="https://web.dev/measure/" target="_blank" class="button">
                Test with Lighthouse
            </a>
            <a href="https://manifest-validator.appspot.com/" target="_blank" class="button">
                Validate Manifest
            </a>
        </div>
        
        <div id="q-manifest-preview" style="display: none; margin-top: 15px;">
            <h3>Manifest Preview</h3>
            <pre id="q-manifest-content" style="background: #f1f1f1; padding: 15px; overflow-x: auto;"></pre>
        </div>
    </div>

    <!-- PWA Installation Instructions -->
    <div class="card" style="margin-top: 20px;">
        <h2>Installation Instructions</h2>
        <p>Once your PWA is configured, users can install it on their devices:</p>
        
        <div class="q-install-instructions">
            <div class="instruction-group">
                <h3>📱 Mobile (Android/iOS)</h3>
                <ol>
                    <li>Open your website in Chrome (Android) or Safari (iOS)</li>
                    <li>Tap the browser menu (⋮ or share button)</li>
                    <li>Select "Add to Home Screen" or "Install App"</li>
                    <li>Follow the prompts to install</li>
                </ol>
            </div>
            
            <div class="instruction-group">
                <h3>💻 Desktop (Chrome/Edge)</h3>
                <ol>
                    <li>Visit your website in Chrome or Edge</li>
                    <li>Look for the install icon in the address bar</li>
                    <li>Click "Install" when prompted</li>
                    <li>The app will be added to your applications</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<style>
.q-pwa-status-dashboard .card {
    padding: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.q-pwa-progress {
    margin: 15px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f1f1f1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00a32a, #4caf50);
    transition: width 0.3s ease;
}

.q-pwa-requirements ul {
    list-style: none;
    padding: 0;
}

.q-pwa-requirements li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f1f1;
}

.q-pwa-requirements li:last-child {
    border-bottom: none;
}

.q-pwa-requirements li.completed {
    color: #00a32a;
}

.q-pwa-requirements li.pending {
    color: #d63638;
}

.q-pwa-requirements .dashicons {
    margin-right: 8px;
}

.q-pwa-requirements small {
    display: block;
    margin-left: 28px;
    color: #666;
    font-style: italic;
}

.q-pwa-actions {
    margin: 20px 0;
}

.q-pwa-actions .button {
    margin-right: 10px;
}

.q-pwa-tools .button {
    margin-right: 10px;
    margin-bottom: 10px;
}

.instruction-group {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.instruction-group h3 {
    margin-top: 0;
}

.instruction-group ol {
    margin-bottom: 0;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Test manifest functionality
    $('#q-test-manifest').on('click', function() {
        const manifestUrl = '<?php echo esc_js(Q_PWA_Manifest::get_manifest_url()); ?>';
        
        $.get(manifestUrl)
            .done(function(data) {
                $('#q-manifest-content').text(JSON.stringify(data, null, 2));
                $('#q-manifest-preview').show();
            })
            .fail(function() {
                alert('Failed to load manifest. Please check your settings.');
            });
    });
    
    // Preview PWA functionality
    $('#q-preview-pwa').on('click', function() {
        window.open('/', '_blank');
    });
    
    // Media upload functionality
    $('.q-media-upload').on('click', function() {
        const field = $(this).data('field');
        const input = $('input[name="' + field + '"]');
        
        const mediaUploader = wp.media({
            title: 'Select App Icon',
            button: {
                text: 'Use this image'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });
        
        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            input.val(attachment.url);
            
            // Show preview
            const preview = input.siblings('img');
            if (preview.length) {
                preview.attr('src', attachment.url);
            } else {
                input.after('<br><img src="' + attachment.url + '" style="max-width: 100px; max-height: 100px; margin-top: 10px;" />');
            }
        });
        
        mediaUploader.open();
    });
});
</script>
